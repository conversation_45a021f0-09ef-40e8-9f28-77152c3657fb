package com.firstbrave.controller.white;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.entity.po.FbmWhitelist;
import org.jeecg.modules.platform.service.IFbmWhitelistService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;

/**
 * @Description: 白名单
 * @Author: fbi
 * @Date: 2025-08-29
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "白名单")
@RestController
@RequestMapping("/fbmWhitelist")
public class FbmWhitelistController extends JeecgController<FbmWhitelist, IFbmWhitelistService> {
    @Resource
    private IFbmWhitelistService fbmWhitelistService;


    /**
     * 分页列表查询
     *
     * @param fbmWhitelist
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "白名单-分页列表查询")
    @ApiOperation(value = "白名单-分页列表查询", notes = "白名单-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(FbmWhitelist fbmWhitelist,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<FbmWhitelist> queryWrapper = QueryGenerator.initQueryWrapper(fbmWhitelist, req.getParameterMap());
        Page<FbmWhitelist> page = new Page<FbmWhitelist>(pageNo, pageSize);
        IPage<FbmWhitelist> pageList = fbmWhitelistService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param fbmWhitelist
     * @return
     */
    @AutoLog(value = "白名单-添加")
    @ApiOperation(value = "白名单-添加", notes = "白名单-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody FbmWhitelist fbmWhitelist) {
        fbmWhitelistService.save(fbmWhitelist);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param fbmWhitelist
     * @return
     */
    @AutoLog(value = "白名单-编辑")
    @ApiOperation(value = "白名单-编辑", notes = "白名单-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody FbmWhitelist fbmWhitelist) {
        fbmWhitelistService.updateById(fbmWhitelist);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "白名单-通过id删除")
    @ApiOperation(value = "白名单-通过id删除", notes = "白名单-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        fbmWhitelistService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "白名单-批量删除")
    @ApiOperation(value = "白名单-批量删除", notes = "白名单-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.fbmWhitelistService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "白名单-通过id查询")
    @ApiOperation(value = "白名单-通过id查询", notes = "白名单-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        FbmWhitelist fbmWhitelist = fbmWhitelistService.getById(id);
        return Result.OK(fbmWhitelist);
    }

}
